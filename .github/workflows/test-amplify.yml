name: Test Amplify Configuration

on:
  workflow_dispatch:
    inputs:
      app_id:
        description: 'Amplify App ID (e.g., d1234567890123)'
        required: true
        type: string
      env_name:
        description: 'Environment name (e.g., staging, main)'
        required: true
        type: string
      region:
        description: 'AWS Region'
        required: true
        default: 'eu-central-1'
        type: choice
        options:
          - eu-central-1
          - us-east-1
          - us-west-2
          - eu-west-1

jobs:
  test-amplify:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ github.event.inputs.region }}

      - name: Install AWS CLI and Amplify CLI
        run: |
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip awscliv2.zip
          sudo ./aws/install --update
          npm install -g @aws-amplify/cli

      - name: Debug AWS Configuration
        run: |
          echo "=== AWS Configuration ==="
          aws sts get-caller-identity
          echo "Region: ${{ github.event.inputs.region }}"
          echo "App ID: ${{ github.event.inputs.app_id }}"
          echo "Env Name: ${{ github.event.inputs.env_name }}"

      - name: List Amplify Apps in Region
        run: |
          echo "=== Listing Amplify Apps in ${{ github.event.inputs.region }} ==="
          aws amplify list-apps --region ${{ github.event.inputs.region }} || echo "No apps found or permission denied"

      - name: Check Specific App
        run: |
          echo "=== Checking specific app ${{ github.event.inputs.app_id }} ==="
          aws amplify get-app --app-id ${{ github.event.inputs.app_id }} --region ${{ github.event.inputs.region }} || echo "App not found"

      - name: List Backend Environments
        run: |
          echo "=== Listing backend environments for app ${{ github.event.inputs.app_id }} ==="
          aws amplify list-backend-environments --app-id ${{ github.event.inputs.app_id }} --region ${{ github.event.inputs.region }} || echo "Cannot list environments"

      - name: Test Amplify Pull with Direct Values
        run: |
          echo "=== Testing Amplify Pull ==="
          AMPLIFY_CONFIG="{\"projectName\":\"test-project\",\"appId\":\"${{ github.event.inputs.app_id }}\",\"envName\":\"${{ github.event.inputs.env_name }}\",\"defaultEditor\":\"code\"}"
          FRONTEND_CONFIG="{\"frontend\":\"javascript\",\"framework\":\"react\",\"config\":{\"SourceDir\":\"src\",\"DistributionDir\":\"build\",\"BuildCommand\":\"npm run build\",\"StartCommand\":\"npm start\"}}"
          PROVIDERS_CONFIG="{\"awscloudformation\":{\"configLevel\":\"project\",\"useProfile\":false,\"accessKeyId\":\"${{ secrets.AWS_ACCESS_KEY_ID }}\",\"secretAccessKey\":\"${{ secrets.AWS_SECRET_ACCESS_KEY }}\",\"region\":\"${{ github.event.inputs.region }}\"}}"
          
          echo "AMPLIFY_CONFIG: $AMPLIFY_CONFIG"
          echo "FRONTEND_CONFIG: $FRONTEND_CONFIG"
          echo "PROVIDERS_CONFIG (masked): {\"awscloudformation\":{\"configLevel\":\"project\",\"useProfile\":false,\"accessKeyId\":\"***\",\"secretAccessKey\":\"***\",\"region\":\"${{ github.event.inputs.region }}\"}}"
          
          amplify pull \
            --amplify "${AMPLIFY_CONFIG}" \
            --frontend "${FRONTEND_CONFIG}" \
            --providers "${PROVIDERS_CONFIG}" \
            --yes

      - name: Check Generated Files
        if: success()
        run: |
          echo "=== Generated Amplify Files ==="
          ls -la
          if [ -f "amplify/.config/project-config.json" ]; then
            echo "Project config:"
            cat amplify/.config/project-config.json
          fi
          if [ -f ".env.local" ]; then
            echo ".env.local exists"
            mv .env.local .env.test
            echo "Moved .env.local to .env.test"
          fi

  test-regions:
    runs-on: ubuntu-latest
    if: failure()
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Install AWS CLI
        run: |
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip awscliv2.zip
          sudo ./aws/install --update

      - name: Search All Common Regions
        run: |
          echo "=== Searching for Amplify apps in all common regions ==="
          for region in us-east-1 us-west-2 eu-west-1 eu-central-1 ap-southeast-1 ap-northeast-1; do
            echo "--- Checking region: $region ---"
            aws amplify list-apps --region $region --query 'apps[].{AppId:appId,Name:name,Region:defaultDomain}' --output table || echo "No access or no apps in $region"
          done
