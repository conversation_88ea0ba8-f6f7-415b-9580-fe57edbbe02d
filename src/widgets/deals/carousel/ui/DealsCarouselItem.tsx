import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { CarouselItem } from '@components/ui/carousel';
import { useIsMobileView } from '@hooks/system';
import { cn } from '@utils/tailwind';
import { useToggle } from 'react-use';

interface DealsCarouselItemProps {
  imgSrc: string;
  title: string;
  ctaLabel: string;
  onCtaClick: () => void;
  isOnlySlide?: boolean;
  shouldLoadOnRedirect?: boolean;
}

export const DealsCarouselItem = ({
  imgSrc,
  title,
  ctaLabel,
  onCtaClick,
  isOnlySlide = false,
  shouldLoadOnRedirect = false,
}: DealsCarouselItemProps) => {
  const isMobileView = useIsMobileView();
  const [isRedirecting, setIsRedirecting] = useToggle(false);

  const onButtonClick = () => {
    if (shouldLoadOnRedirect) {
      setIsRedirecting();
      setTimeout(() => {
        setIsRedirecting(false);
      }, 2000);
    }
    onCtaClick();
  };

  return (
    <CarouselItem
      key={title}
      className={cn(
        'mr-1.5 basis-[85%] pl-4',
        isOnlySlide && 'mr-0 basis-full pl-4',
      )}
    >
      <div
        style={{
          backgroundImage: `linear-gradient(0deg, rgba(27, 22, 25, 0.40) 0%, rgba(27, 22, 25, 0.40) 100%), url(${imgSrc})`,
        }}
        className="relative flex h-[20rem] w-full flex-col justify-end rounded-2xl bg-center bg-cover px-6 py-8 md:bg-center md:bg-no-repeat"
      >
        <div
          className="absolute top-0 left-0 h-full w-full rounded-2xl"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='3' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%' height='100%' filter='url(%23noise)' opacity='0.45'/%3E%3C/svg%3E")`,
            mixBlendMode: 'overlay',
          }}
        />
        <Typography
          variant={isMobileView ? 'xs' : 'm'}
          className="max-w-[31.75rem] text-white"
        >
          {title}
        </Typography>
        <Button
          loading={isRedirecting}
          onClick={onButtonClick}
          size="small"
          variant="white"
          className="relative z-10 mt-6 w-fit"
        >
          {ctaLabel}
        </Button>
      </div>
    </CarouselItem>
  );
};
